const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const bcrypt = require("bcryptjs");
const db = require("./../connection");
exports.getAllUsers = catchAsync(async (req, res, next) => {
	const q = `SELECT id, username, email, f_name, l_name, img, role, governorate_id, province_id FROM users`;
	const data = await db.query(q);
	res.status(200).json({
		status: "success",
		data,
	});
});
exports.getUserStations = catchAsync(async (req, res, next) => {
	const q = `SELECT station_id FROM user_stations WHERE user_id = ?`;
	const value = [req.params.id];

	const data = await db.query(q, value);
	res.status(200).json({
		status: "success",
		data,
	});
});

exports.deleteUser = catchAsync(async (req, res, next) => {
	if (req.params.id === "1") {
		return next(new AppError("لا يمكن حذف هذا المستخدم", 500));
	}
	const q = `DELETE FROM users WHERE id= ? `;
	const value = [req.params.id];
	await db.query(q, value);
	res.status(200).json({ state: "success" });
});

exports.updateUser = catchAsync(async (req, res, next) => {
	const salt = bcrypt.genSaltSync(10);
	const hashedPassword = bcrypt.hashSync(req.body.password, salt);
	q = `UPDATE users SET username = ?  , password =? ,email=? ,f_name=?,l_name=?,governorate_id=?,province_id=?,role=? WHERE id = ?`;
	const value = [
		req.body.username,
		hashedPassword,
		req.body.email,
		req.body.f_name,
		req.body.l_name,
		req.body.governorate_id,
		req.body.province_id,
		req.body.role,
		req.params.id,
	];
	let queryArray = [db.query(q, value)];
	req.body.stations.stationsToAdd.forEach((el) =>
		queryArray.push(
			db.query(
				`INSERT INTO user_stations (user_id,station_id) VALUES (${req.params.id},${el})`
			)
		)
	);
	req.body.stations.stationsToRemove.forEach((el) =>
		queryArray.push(
			db.query(
				`DELETE FROM user_stations WHERE user_id =${req.params.id} AND station_id=${el}`
			)
		)
	);
	await db.beginTransactionPromise();
	await Promise.all(queryArray);
	await db.commitPromise();
	res.status(200).json({ state: "success" });
});
