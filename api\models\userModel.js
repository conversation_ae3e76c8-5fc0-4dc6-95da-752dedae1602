// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const UserModel = sequelize.define(
// 	"user",
// 	{
// 		// Model attributes are defined here
// 		username: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		password: {
// 			type: DataTypes.STRING,
// 			allowNull: false,
// 		},
// 		first_name: {
// 			type: DataTypes.STRING,
// 		},
// 		last_name: {
// 			type: DataTypes.STRING,
// 		},
// 	},
// 	{ timestamps: false }
// );
// module.exports = UserModel;

const { DataTypes } = require("sequelize");
function defineUserModel(sequelize, models) {
	const UserModel = sequelize.define("user", {
		username: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		password: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		first_name: {
			type: DataTypes.STRING,
		},
		last_name: {
			type: DataTypes.STRING,
		},
	});
	return UserModel;
}

module.exports = defineUserModel;
