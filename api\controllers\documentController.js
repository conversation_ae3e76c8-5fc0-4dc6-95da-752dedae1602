const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const multer = require("multer");
const path = require("path");
const fs = require("fs").promises;
const { fromPath } = require("pdf2pic");
const { exec } = require("child_process");
const { getModel } = require("../utils/modelSelect");

exports.getAllDocuments = catchAsync(async (req, res, next) => {
	const DocumentModel = getModel(req.headers["x-year"], "DocumentModel");
	try {
		const documents = await DocumentModel.findAll({
			// include: { model: SectionModel, attributes: ["id", "name"] },
			order: [["id", "DESC"]],
			limit: +req.query.limit || null,
			offset: +req.query.limit * +req.query.page || null,
		});
		const total = await DocumentModel.count({});
		res.status(200).json({
			state: "success",
			documents,
			total,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
function execPromise(cmd) {
	return new Promise((resolve, reject) => {
		exec(cmd, (error, stdout, stderr) => {
			if (error) return reject(error);
			resolve({ stdout, stderr });
		});
	});
}
exports.viewDocument = catchAsync(async (req, res, next) => {
	const DocumentModel = getModel(req.headers["x-year"], "DocumentModel");
	const IncomeModel = getModel(req.headers["x-year"], "IncomeModel");
	const OutcomeModel = getModel(req.headers["x-year"], "OutcomeModel");
	try {
		const document = await DocumentModel.findByPk(req.params.id, { raw: true });
		if (!document) {
			return next(new AppError("لم يتم العثور على المستند", 404));
		}
		let detailes = null;
		if (document.type === "وارد") {
			const income = await IncomeModel.findOne({
				where: { document_id: document.id },
				raw: true,
			});
			detailes = income;
		} else if (document.type === "صادر") {
			const outcome = await OutcomeModel.findOne({
				where: { document_id: document.id },
				raw: true,
			});
			detailes = outcome;
		}

		res.status(200).json({
			state: "success",
			document: { ...document, detailes },
		});
	} catch (error) {
		return next(new AppError("الملف غير موجود على السيرفر", 404));
	}
});

// Configure multer for handling file uploads
const storage = multer.diskStorage({
	destination: function (req, file, cb) {
		cb(null, "uploads/"); // Make sure this directory exists
	},
	filename: function (req, file, cb) {
		cb(null, Date.now() + path.extname(file.originalname));
	},
});

exports.addDocument = catchAsync(async (req, res, next) => {
	const DocumentModel = getModel(req.headers["x-year"], "DocumentModel");
	const IncomeModel = getModel(req.headers["x-year"], "IncomeModel");
	const OutcomeModel = getModel(req.headers["x-year"], "OutcomeModel");
	const imagePaths = []; // Move declaration outside try block
	let document; // Declare document variable outside try block to access it in catch

	try {
		await req.db.transaction(async (t) => {
			for (const file of req.files) {
				const imagePath = path.join("uploads", file.originalname);
				await fs.writeFile(imagePath, file.buffer);
				imagePaths.push(imagePath);
			}
			document = await DocumentModel.create(
				{
					title: req.body.title,
					type: req.body.type,
					document_date: req.body.documentDate,
					adding_date: req.body.addingDate,
					section_id: req.body.section,
					cabinet_id: req.body.cabinet,
					shelf_id: req.body.shelf,
					folder_id: req.body.folder,
				},
				{ transaction: t }
			);

			if (req.body.type === "وارد") {
				data = await IncomeModel.create(
					{
						Sn: req.body.Sn,
						from_Sn: req.body.fromSn,
						from: req.body.from,
						to: req.body.to,
						recipient: req.body.recipient,
						document_id: document.id,
					},
					{ transaction: t }
				);
			} else if (req.body.type === "صادر") {
				data = await OutcomeModel.create(
					{
						Sn: +req.body.Sn,
						from: req.body.from,
						to: req.body.to,
						recipient: req.body.recipient,
						document_id: +document.id,
					},
					{ transaction: t }
				);
			}
			// Create PDF using NAPS2
			if (imagePaths.length > 0) {
				await execPromise(
					`naps2.console -i ${imagePaths.join(";")} -n 0 -o "D:/Archive/${
						req.body.section
					}/${req.body.cabinet}/${req.body.shelf}/${req.body.folder}/${
						document.id
					}.pdf"`
				);
			} // Clean up temporary files
			for (const imagePath of imagePaths) {
				await fs.unlink(imagePath);
			}
		});
		res.status(200).json({
			status: "success",
			message: "Document added successfully",
		});
	} catch (error) {
		// Clean up any remaining files in case of error
		if (imagePaths) {
			for (const imagePath of imagePaths) {
				try {
					await fs.unlink(imagePath);
				} catch (cleanupError) {
					console.error("Error cleaning up files:", cleanupError);
				}
			}
		}
		// Delete PDF file if it was created
		if (document) {
			try {
				const pdfPath = `D:/Archive/${req.body.section}/${req.body.cabinet}/${req.body.shelf}/${req.body.folder}/${document.id}.pdf`;
				await fs.unlink(pdfPath);
				await DocumentModel.destroy({
					where: { id: document.id },
				});
			} catch (error) {
				console.error("Error deleting PDF file:", error);
			}
		}
		return next(new AppError(error, 500));
	}
});
exports.editDocument = catchAsync(async (req, res, next) => {
	const DocumentModel = getModel(req.headers["x-year"], "DocumentModel");
	const IncomeModel = getModel(req.headers["x-year"], "IncomeModel");
	const OutcomeModel = getModel(req.headers["x-year"], "OutcomeModel");
	const imagePaths = [];
	let document;
	try {
		await req.db.transaction(async (t) => {
			// 1. Find and check the document
			document = await DocumentModel.findByPk(req.params.id, {
				transaction: t,
			});
			if (!document) {
				return next(new AppError("لم يتم العثور على المستند", 404));
			}

			// 2. Get PDF path
			const pdfPath = `D:/Archive/${document.section_id}/${document.cabinet_id}/${document.shelf_id}/${document.folder_id}/${document.id}.pdf`;

			// 3. Delete old PDF file if exists
			try {
				await fs.unlink(pdfPath);
			} catch (err) {
				// If file doesn't exist, that's fine
				if (err.code !== "ENOENT") throw err;
			}

			// 4. Save incoming images to disk temporarily
			for (const file of req.files) {
				const imagePath = path.join("uploads", file.originalname);
				await fs.writeFile(imagePath, file.buffer);
				imagePaths.push(imagePath);
			}

			// 5. Update document meta-info if fields are being edited
			await document.update(
				{
					title: req.body.title,
					type: req.body.type,
					document_date: req.body.documentDate,
					adding_date: req.body.addingDate,
					section_id: req.body.section,
					cabinet_id: req.body.cabinet,
					shelf_id: req.body.shelf,
					folder_id: req.body.folder,
				},
				{ transaction: t }
			);

			// 6. Update type-specific meta tables
			if (req.body.type === "وارد") {
				await IncomeModel.update(
					{
						Sn: req.body.Sn,
						from_Sn: req.body.fromSn,
						from: req.body.from,
						to: req.body.to,
						recipient: req.body.recipient,
					},
					{ where: { document_id: document.id }, transaction: t }
				);
			} else if (req.body.type === "صادر") {
				await OutcomeModel.update(
					{
						Sn: +req.body.Sn,
						from: req.body.from,
						to: req.body.to,
						recipient: req.body.recipient,
					},
					{ where: { document_id: document.id }, transaction: t }
				);
			}

			// 7. Generate the new PDF using NAPS2 from the new images
			if (imagePaths.length > 0) {
				await execPromise(
					`naps2.console -i ${imagePaths.join(";")} -n 0 -o "${pdfPath}"`
				);
			}

			// 8. Clean up temporary image files
			for (const imagePath of imagePaths) {
				await fs.unlink(imagePath);
			}
		});

		res.status(200).json({
			status: "success",
			message: "Document edited successfully",
		});
	} catch (error) {
		// Clean up temp image files
		for (const imagePath of imagePaths) {
			try {
				await fs.unlink(imagePath);
			} catch (cleanupError) {
				console.error("Error cleaning up files:", cleanupError);
			}
		}

		// Delete incomplete PDF or leave as-is if desired
		if (document) {
			try {
				const pdfPath = `D:/Archive/${document.section_id}/${document.cabinet_id}/${document.shelf_id}/${document.folder_id}/${document.id}.pdf`;
				await fs.unlink(pdfPath);
			} catch (err) {
				// Ignore if file didn't exist
			}
		}

		return next(new AppError(error, 500));
	}
});

exports.pdf2imgs = catchAsync(async (req, res, next) => {
	const DocumentModel = getModel(req.headers["x-year"], "DocumentModel");
	const document = await DocumentModel.findByPk(req.params.id, { raw: true });
	if (!document) {
		return next(new AppError("لم يتم العثور على المستند", 404));
	}
	const pdfPath = `D:/Archive/${document.section_id}/${document.cabinet_id}/${document.shelf_id}/${document.folder_id}/${document.id}.pdf`;
	const outputDir = "C:/pdf2pic";
	const timestamp = new Date().toISOString().replace(/[-:T]/g, "").slice(0, 15);
	const safeTitle = document.title.replace(/[^\\w\\u0600-\\u06FF]+/g, "_");
	// 1. Clean output directory
	cleanOutputDir(outputDir);

	// 2. Convert PDF to images
	const pdf2pic = fromPath(pdfPath, {
		density: 200,
		savePath: outputDir,
		format: "png",
		saveFilename: "page",
	});
	const result = await pdf2pic.bulk(-1);
	await Promise.all(
		result.map(async (pageRes, idx) => {
			// Example: MyDocTitle_20250721_061636_1.png
			const newName = `${safeTitle}_${timestamp}_${idx + 1}.png`;
			const newPath = path.join(outputDir, newName);
			await fs.rename(pageRes.path, newPath);
			pageRes.path = newPath;
			pageRes.filename = newName; // For downstream use
		})
	);
	// 3. Encode images as base64 and prepare payload
	const images = await Promise.all(
		result.map(async (pageRes, idx) => {
			const buf = await fs.readFile(pageRes.path);
			return {
				filename: pageRes.filename, // The new filename, e.g. Title_20250721_061636_1.png
				data: buf.toString("base64"),
			};
		})
	);

	// 4. Send JSON response
	res.status(200).json({
		state: "success",
		images,
	});
});

async function cleanOutputDir(outputDir) {
	try {
		// Test if directory exists
		await fs.access(outputDir).catch(async () => {
			await fs.mkdir(outputDir, { recursive: true });
		});

		const files = await fs.readdir(outputDir);
		for (const file of files) {
			const curPath = path.join(outputDir, file);
			const stat = await fs.lstat(curPath);
			if (stat.isDirectory()) {
				await fs.rm(curPath, { recursive: true, force: true });
			} else {
				await fs.unlink(curPath);
			}
		}
	} catch (err) {
		console.error("Error cleaning outputDir:", err);
	}
}
// exports.deleteDocument = catchAsync(async (req, res, next) => {
// 	const filePath = `D:/Archive/${req.params.cat_id}/${req.params.doc_id}.pdf`;

// 	fs.unlink(filePath, (err) => {
// 		if (err) {
// 			if (err.errno === -4058) {
// 				return next(new AppError("الملف غير موجود", 500));
// 			} else {
// 				return next(new AppError("حصل خطأ أثناء عملية حذف الملف", 500));
// 			}
// 		}
// 	});
// 	await db.query(`DELETE FROM documents WHERE id=${req.params.doc_id}`);
// 	res.status(200).json({
// 		state: "success",
// 	});
// });
