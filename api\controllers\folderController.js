const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const { Op } = require("sequelize");
const { getModel } = require("../utils/modelSelect");

exports.getAllFolders = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	const SectionModel = getModel(req.headers["x-year"], "SectionModel");
	const ShelfModel = getModel(req.headers["x-year"], "ShelfModel");
	const FolderModel = getModel(req.headers["x-year"], "FolderModel");
	try {
		const whereClause = {};
		if (
			req.query.shelfId &&
			req.query.shelfId !== "undefined" &&
			req.query.shelfId !== "null"
		) {
			// split to array
			const ids = req.query.shelfId.split(",").map((id) => +id.trim());
			// Use Op.in for array, Op.eq for single
			whereClause.shelf_id = ids.length > 1 ? { [Op.in]: ids } : ids[0];
		}
		const folders = await FolderModel.findAll({
			where: whereClause,
			include: [
				{ model: SectionModel, attributes: ["id", "name"] },
				{ model: CabinetModel, attributes: ["id", "name"] },
				{ model: ShelfModel, attributes: ["id", "name"] },
			],
			order: [["createdAt", "DESC"]],
			limit: +req.query.limit || null,
			offset: +req.query.limit * +req.query.page || null,
		});
		const total = await FolderModel.count({});
		res.status(200).json({
			state: "success",
			folders,
			total,
		});
	} catch (error) {
		console.log(`error`, error);
		return next(new AppError(error, 500));
	}
});
exports.getFolder = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	const SectionModel = getModel(req.headers["x-year"], "SectionModel");
	const ShelfModel = getModel(req.headers["x-year"], "ShelfModel");
	const FolderModel = getModel(req.headers["x-year"], "FolderModel");
	try {
		const folder = await FolderModel.findByPk(req.params.id, {
			include: [
				{ model: SectionModel, attributes: ["id", "name"] },
				{ model: CabinetModel, attributes: ["id", "name"] },
				{ model: ShelfModel, attributes: ["id", "name"] },
			],
		});
		res.status(200).json({
			state: "success",
			folder,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.addFolder = catchAsync(async (req, res, next) => {
	const FolderModel = getModel(req.headers["x-year"], "FolderModel");
	console.log(`req.body`, req.body);
	try {
		await req.db.transaction(async (t) => {
			await FolderModel.create(
				{
					name: req.body.name,
					section_id: req.body.section_id,
					cabinet_id: req.body.cabinet_id,
					shelf_id: req.body.shelf_id,
				},
				{ transaction: t }
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		console.log(`error`, error);
		return next(new AppError(error, 500));
	}
});
exports.deleteFolder = catchAsync(async (req, res, next) => {
	const FolderModel = getModel(req.headers["x-year"], "FolderModel");
	try {
		await req.db.transaction(async (t) => {
			await FolderModel.destroy(
				{
					where: { id: req.params.id },
				},
				{ transaction: t }
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.updateFolder = catchAsync(async (req, res, next) => {
	const FolderModel = getModel(req.headers["x-year"], "FolderModel");
	try {
		await req.db.transaction(async (t) => {
			await FolderModel.update(
				{
					name: req.body.name,
					section_id: req.body.section_id,
					cabinet_id: req.body.cabinet_id,
					shelf_id: req.body.shelf_id,
				},
				{
					where: { id: req.params.id },
					transaction: t,
				}
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
