const express = require("express");
const CabinetController = require("./../controllers/cabinetController");
const router = express.Router();
router
	.route("")
	.get(CabinetController.getAllCabinets)
	.post(CabinetController.addCabinet);
router
	.route("/:id")
	.get(CabinetController.getCabinet)
	.patch(CabinetController.updateCabinet)
	.delete(CabinetController.deleteCabinet);

module.exports = router;
