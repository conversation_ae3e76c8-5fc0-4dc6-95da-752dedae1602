const catchAsync = require("../utils/catchAsync");
const db = require("./../connection");
const util = require("util");
const fs = require("fs");
const { promisify } = require("util");
const IncomeModel = require("../models/incomeModel");
const AppError = require("../utils/appError");
const exec = promisify(require("child_process").exec);

exports.getLastIncomeSn = catchAsync(async (req, res, next) => {
	try {
		let income = await IncomeModel.findOne({
			raw: true,
			order: [["Sn", "DESC"]],
		});
		if (!income) {
			income = 0;
		} else {
			income = income.Sn;
		}

		console.log(`income`, income);
		res.status(200).json({
			state: "success",
			income: income,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
// exports.getAllIncome = catchAsync(async (req, res, next) => {
// 	const incomes = await db.query(`SELECT * FROM  income ORDER BY num DESC`);

// 	res.status(200).json({
// 		state: "success",
// 		incomes,
// 	});
// });
// exports.addIncome = catchAsync(async (req, res, next) => {
// 	console.log(`req.body`, req.body);
// 	let document = { insertId: null };
// 	try {
// 		await db.beginTransactionPromise();
// 		if (req.body.hasArchive) {
// 			const q1 = `INSERT INTO documents (title,category_id,adding_date,document_date) VALUES (?)`;
// 			const values1 = [
// 				req.body.title,
// 				req.body.categoryId,
// 				req.body.date,
// 				req.body.fromDate,
// 			];
// 			document = await db.query(q1, [values1]);
// 			const text = req.body.imgs.map((el) => `D:/TEMP/${el}`);
// 			const { stdout, stderr } = await exec(
// 				`naps2.console -i ${text.join(";")} -n 0 -o "D:/Archive/${
// 					req.body.categoryId
// 				}/${document.insertId}.pdf"`
// 			);
// 		}
// 		const q2 = `INSERT INTO income (num, date, \`from\`, from_num, from_date, title, \`to\`, recipient, note,has_archive,document_id,category_id) VALUES (?)`;
// 		const values2 = [
// 			+req.body.num,
// 			req.body.date,
// 			req.body.from,
// 			req.body.fromNum,
// 			req.body.fromDate,
// 			req.body.title,
// 			req.body.to,
// 			req.body.recipient,
// 			req.body.note,
// 			req.body.hasArchive,
// 			document.insertId,
// 			req.body.categoryId,
// 		];
// 		income = await db.query(q2, [values2]);
// 		if (req.body.hasArchive) {
// 			const q3 = `UPDATE documents SET income_id=? WHERE id=?`;
// 			const values3 = [income.insertId, document.insertId];
// 			await db.query(q3, values3);
// 		}
// 		await db.commitPromise();
// 		res.status(200).json({
// 			state: "success",
// 		});
// 	} catch (error) {
// 		await db.rollbackPromise();
// 		console.log(`error`, error);
// 		res.status(500).json({
// 			state: "error",
// 			error,
// 		});
// 	}
// });
// exports.editIncome = catchAsync(async (req, res, next) => {
// 	q = `UPDATE income SET num = ?  , date =? ,\`from\`=? ,from_num=?,from_date=?,title=?,\`to\`=?,recipient=?,note=?,has_archive=? WHERE id = ${+req
// 		.params.id}`;
// 	const values = [
// 		+req.body.num,
// 		req.body.date,
// 		req.body.from,
// 		req.body.fromNum,
// 		req.body.fromDate,
// 		req.body.title,
// 		req.body.to,
// 		req.body.recipient,
// 		req.body.note,
// 		req.body.hasArchive,
// 	];
// 	await db.query(q, values);
// 	res.status(200).json({
// 		state: "success",
// 	});
// });
// exports.deleteIncome = catchAsync(async (req, res, next) => {
// 	try {
// 		await db.beginTransactionPromise();
// 		const document = await db.query(
// 			`SELECT * FROM documents WHERE income_id=${req.params.id}`
// 		);
// 		const income = await db.query(
// 			`SELECT * FROM income WHERE id=${req.params.id}`
// 		);

// 		if (income[0].has_archive === 1) {
// 			const filePath = `D:/Archive/${document[0].category_id}/${document[0].id}.pdf`;
// 			fs.unlink(filePath, (err) => {
// 				if (err) {
// 					if (err.errno === -4058) {
// 						return next(new AppError("الملف غير موجود", 500));
// 					} else {
// 						return next(new AppError("حصل خطأ أثناء عملية حذف الملف", 500));
// 					}
// 				}
// 			});
// 			await db.query(`DELETE FROM documents WHERE id=${document[0].id}`);
// 		}
// 		await db.query(`DELETE FROM income WHERE id=${req.params.id}`);
// 		await db.commitPromise();
// 		res.status(200).json({
// 			state: "success",
// 		});
// 	} catch (error) {
// 		await db.rollbackPromise();
// 		console.log(`error`, error);
// 		res.status(500).json({
// 			state: "error",
// 			error,
// 		});
// 	}
// });
