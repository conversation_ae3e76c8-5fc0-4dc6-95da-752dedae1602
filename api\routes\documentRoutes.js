const express = require("express");
const documentController = require("./../controllers/documentController");
const router = express.Router();
const multer = require("multer");
const upload = multer({
	storage: multer.memoryStorage(), // Store files in memory
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
	},
});
router.route("").get(documentController.getAllDocuments);
router.post("/", upload.array("images"), documentController.addDocument);
router
	.route("/:id")
	.get(documentController.viewDocument)
	.patch(documentController.editDocument);
router.route("/pdf2imgs/:id").get(documentController.pdf2imgs);
module.exports = router;
