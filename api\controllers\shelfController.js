const CabinetModel = require("../models/cabinetModel");
const SectionModel = require("../models/sectionModel");
const ShelfModel = require("../models/shelfModel");
const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const { Op } = require("sequelize");
exports.getAllShelves = catchAsync(async (req, res, next) => {
	try {
		const whereClause = {};
		console.log(`req.query`, req.query);
		if (
			req.query.cabinetId &&
			req.query.cabinetId !== "undefined" &&
			req.query.cabinetId !== "null"
		) {
			// split to array
			const ids = req.query.cabinetId.split(",").map((id) => +id.trim());
			// Use Op.in for array, Op.eq for single
			whereClause.cabinet_id = ids.length > 1 ? { [Op.in]: ids } : ids[0];
		}
		const shelves = await ShelfModel.findAll({
			where: whereClause,
			include: [
				{ model: SectionModel, attributes: ["id", "name"] },
				{ model: CabinetModel, attributes: ["id", "name"] },
			],
			order: [["createdAt", "DESC"]],
			limit: +req.query.limit || null,
			offset: +req.query.limit * +req.query.page || null,
		});
		const total = await ShelfModel.count({});
		res.status(200).json({
			state: "success",
			shelves,
			total,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.getShelf = catchAsync(async (req, res, next) => {
	try {
		const shelf = await ShelfModel.findByPk(req.params.id, {
			include: [
				{ model: SectionModel, attributes: ["id", "name"] },
				{ model: CabinetModel, attributes: ["id", "name"] },
			],
		});
		res.status(200).json({
			state: "success",
			shelf,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.addShelf = catchAsync(async (req, res, next) => {
	try {
		console.log(req.body.shelf_id);
		await ShelfModel.create({
			name: req.body.name,
			section_id: req.body.section_id,
			cabinet_id: req.body.cabinet_id,
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.deleteShelf = catchAsync(async (req, res, next) => {
	try {
		await ShelfModel.destroy({
			where: { id: req.params.id },
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.updateShelf = catchAsync(async (req, res, next) => {
	try {
		await ShelfModel.update(
			{
				name: req.body.name,
				section_id: req.body.section_id,
				cabinet_id: req.body.cabinet_id,
			},
			{
				where: { id: req.params.id },
			}
		);
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
