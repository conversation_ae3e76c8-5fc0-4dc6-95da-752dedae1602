// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const SectionModel = require("./sectionModel");
// const CabinetModel = sequelize.define("cabinet", {
// 	name: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	section_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// });
// CabinetModel.belongsTo(SectionModel, { foreignKey: "section_id" });
// module.exports = CabinetModel;

const { DataTypes } = require("sequelize");
function defineCabinetModel(sequelize, models) {
	const CabinetModel = sequelize.define("cabinet", {
		name: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		section_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
	});

	// Setup associations
	if (models) {
		if (models.SectionModel) {
			CabinetModel.belongsTo(models.SectionModel, {
				foreignKey: "section_id",
			});
		}
	}

	return CabinetModel;
}

module.exports = defineCabinetModel;
