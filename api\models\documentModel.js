// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const FolderModel = require("./folderModel");
// const DocumentModel = sequelize.define("document", {
// 	title: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	document_date: {
// 		type: DataTypes.DATEONLY,
// 		allowNull: false,
// 	},
// 	adding_date: {
// 		type: DataTypes.DATEONLY,
// 		allowNull: false,
// 	},
// 	section_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	cabinet_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	shelf_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	folder_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	income_outcome_id: { type: DataTypes.INTEGER },
// 	type: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// });
// DocumentModel.belongsTo(FolderModel, { foreignKey: "folder_id" });
// DocumentModel.belongsTo(FolderModel, { foreignKey: "section_id" });
// DocumentModel.belongsTo(FolderModel, { foreignKey: "folder_id" });
// module.exports = DocumentModel;

const { DataTypes } = require("sequelize");
function defineDocumentModel(sequelize, models) {
	const DocumentModel = sequelize.define(
		"document",
		{
			title: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			document_date: {
				type: DataTypes.DATEONLY,
				allowNull: false,
			},
			adding_date: {
				type: DataTypes.DATEONLY,
				allowNull: false,
			},
			section_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			cabinet_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			shelf_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			folder_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			income_outcome_id: { type: DataTypes.INTEGER },
			type: {
				type: DataTypes.STRING,
				allowNull: false,
			},
		},
		{
			timestamps: false,
		}
	);

	// Setup associations
	if (models) {
		if (models.SectionModel) {
			DocumentModel.belongsTo(models.SectionModel, {
				foreignKey: "section_id",
			});
		}
		if (models.CabinetModel) {
			DocumentModel.belongsTo(models.CabinetModel, {
				foreignKey: "cabinet_id",
			});
		}
		if (models.ShelfModel) {
			DocumentModel.belongsTo(models.ShelfModel, {
				foreignKey: "shelf_id",
			});
		}
		if (models.FolderModel) {
			DocumentModel.belongsTo(models.FolderModel, {
				foreignKey: "folder_id",
			});
		}
	}

	return DocumentModel;
}

module.exports = defineDocumentModel;
