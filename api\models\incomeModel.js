// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const DocumentModel = require("./documentModel");
// const IncomeModel = sequelize.define("income", {
// 	Sn: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 		unique: true,
// 	},
// 	from: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	from_Sn: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	to: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	recipient: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	document_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// });
// IncomeModel.belongsTo(DocumentModel, { foreignKey: "document_id" });
// module.exports = IncomeModel;

const { DataTypes } = require("sequelize");
function defineIncomeModel(sequelize, models) {
	const IncomeModel = sequelize.define("income", {
		Sn: {
			type: DataTypes.INTEGER,
			allowNull: false,
			unique: true,
		},
		from_Sn: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		from: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		to: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		recipient: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		document_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
	});

	// Setup associations
	if (models) {
		if (models.DocumentModel) {
			IncomeModel.belongsTo(models.DocumentModel, {
				foreignKey: "document_id",
			});
		}
	}

	return IncomeModel;
}

module.exports = defineIncomeModel;
