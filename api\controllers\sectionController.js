const SectionModel = require("../models/sectionModel");
const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
exports.getAllSections = catchAsync(async (req, res, next) => {
	try {
		const sections = await SectionModel.findAll({
			order: [["createdAt", "DESC"]],
			limit: +req.query.limit || null,
			offset: +req.query.limit * +req.query.page || null,
		});
		const total = await SectionModel.count({});
		res.status(200).json({
			state: "success",
			sections,
			total,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.getSection = catchAsync(async (req, res, next) => {
	try {
		const section = await SectionModel.findByPk(req.params.id);
		res.status(200).json({
			state: "success",
			section,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.addSection = catchAsync(async (req, res, next) => {
	try {
		await SectionModel.create({
			name: req.body.name,
			description: req.body.description,
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.deleteSection = catchAsync(async (req, res, next) => {
	try {
		await SectionModel.destroy({
			where: { id: req.params.id },
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.updateSection = catchAsync(async (req, res, next) => {
	try {
		await SectionModel.update(
			{ name: req.body.name, description: req.body.description },
			{
				where: { id: req.params.id },
			}
		);
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
