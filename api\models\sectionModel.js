// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const SectionModel = sequelize.define("section", {
// 	name: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	description: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// });
// module.exports = SectionModel;

const { DataTypes } = require("sequelize");
function defineSectionModel(sequelize, models) {
	const SectionModel = sequelize.define(
		"section",
		{
			name: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false,
			},
		},
		{
			timestamps: false,
		}
	);

	return SectionModel;
}

module.exports = defineSectionModel;
