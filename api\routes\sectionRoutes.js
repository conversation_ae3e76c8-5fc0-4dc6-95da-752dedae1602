const express = require("express");
const SectionController = require("./../controllers/sectionController");
const router = express.Router();
router
	.route("")
	.get(SectionController.getAllSections)
	.post(SectionController.addSection);
router
	.route("/:id")
	.get(SectionController.getSection)
	.patch(SectionController.updateSection)
	.delete(SectionController.deleteSection);

module.exports = router;
