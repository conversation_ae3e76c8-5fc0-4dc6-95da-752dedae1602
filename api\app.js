const express = require("express");

const globalErrorHandler = require("./controllers/errorController");
const authRouter = require("./routes/authRoutes");
const userRouter = require("./routes/userRoutes");
const authController = require("./controllers/authController");
const incomeRouter = require("./routes/incomeRoutes");
const outcomeRouter = require("./routes/outcomeRoutes");
const documentRouter = require("./routes/documentRoutes");
// const categoryRouter = require("./routes/categoryRoutes");

const scanersRouter = require("./routes/scanersRoutes");
const sectionRouter = require("./routes/sectionRoutes");
const cabinetRouter = require("./routes/cabinetRoutes");
const shelfRouter = require("./routes/shelfRoutes");
const folderRouter = require("./routes/folderRoutes");
const cors = require("cors");
const AppError = require("./utils/appError");
const corsOptions = {
	origin: "http://localhost:5173",
	credentials: true, // Match your manual header setting
	optionsSuccessStatus: 200,
};

// Get a list of available devices
const app = express();
// Apply CORS before other middleware
app.use(cors(corsOptions));

// Serve static files with proper headers for PDFs
// Add this middleware before your static file serving
app.use("/archive", (req, res, next) => {
	// Set headers to prevent download and enable inline viewing
	res.setHeader("Content-Type", "application/pdf");
	res.setHeader("Content-Disposition", "inline");
	res.setHeader("X-Content-Type-Options", "nosniff");
	next();
});

app.use(express.json());
app.use("/images", express.static("D:/TEMP"));
app.use("/archive", express.static("D:/Archive"));
app.use("/api/v1/auth", authRouter);
app.all("/*", authController.protect);
app.use("/api/v1/users", userRouter);
app.use("/api/v1/incomes", incomeRouter);
app.use("/api/v1/outcomes", outcomeRouter);
app.use("/api/v1/documents", documentRouter);
// app.use("/api/v1/category", categoryRouter);
app.use("/api/v1/scaners", scanersRouter);
app.use("/api/v1/sections", sectionRouter);
app.use("/api/v1/cabinets", cabinetRouter);
app.use("/api/v1/shelves", shelfRouter);
app.use("/api/v1/folders", folderRouter);
app.all("/*", (req, res, next) => {
	next(new AppError(`cant find ${req.originalUrl} on this server`, 404));
});
app.use(globalErrorHandler);
module.exports = app;
