// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const DocumentModel = require("./documentModel");
// const OutcomeModel = sequelize.define("outcome", {
// 	Sn: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 		unique: true,
// 	},
// 	from: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	to: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	recipient: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	document_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// });
// OutcomeModel.belongsTo(DocumentModel, { foreignKey: "document_id" });
// module.exports = OutcomeModel;

const { DataTypes } = require("sequelize");
function defineOutcomeModel(sequelize, models) {
	const OutcomeModel = sequelize.define("outcome", {
		Sn: {
			type: DataTypes.INTEGER,
			allowNull: false,
			unique: true,
		},
		from: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		to: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		recipient: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		document_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
	});

	// Setup associations
	if (models) {
		if (models.DocumentModel) {
			OutcomeModel.belongsTo(models.DocumentModel, {
				foreignKey: "document_id",
			});
		}
	}

	return OutcomeModel;
}

module.exports = defineOutcomeModel;
