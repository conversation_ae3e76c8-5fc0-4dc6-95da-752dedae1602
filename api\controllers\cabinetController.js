const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const { Op } = require("sequelize");
const { getModel } = require("../utils/modelSelect");
exports.getAllCabinets = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	const SectionModel = getModel(req.headers["x-year"], "SectionModel");

	try {
		const whereClause = {};
		if (
			req.query.sectionId &&
			req.query.sectionId !== "undefined" &&
			req.query.sectionId !== "null"
		) {
			// split to array
			const ids = req.query.sectionId.split(",").map((id) => +id.trim());
			// Use Op.in for array, Op.eq for single
			whereClause.section_id = ids.length > 1 ? { [Op.in]: ids } : ids[0];
		}

		const cabinets = await CabinetModel.findAll({
			where: whereClause,
			include: { model: SectionModel, attributes: ["id", "name"] },
			order: [["createdAt", "DESC"]],
			limit: +req.query.limit || null,
			offset: +req.query.limit * +req.query.page || null,
		});
		const total = await CabinetModel.count({});
		res.status(200).json({
			state: "success",
			cabinets,
			total,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.getCabinet = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	const SectionModel = getModel(req.headers["x-year"], "SectionModel");
	try {
		const cabinet = await CabinetModel.findByPk(req.params.id, {
			include: { model: SectionModel, attributes: ["id", "name"] },
		});
		res.status(200).json({
			state: "success",
			cabinet,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

exports.addCabinet = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	try {
		await req.db.transaction(async (t) => {
			await CabinetModel.create(
				{
					name: req.body.name,
					section_id: req.body.section_id,
				},
				{ transaction: t }
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.deleteCabinet = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	try {
		await req.db.transaction(async (t) => {
			await CabinetModel.destroy(
				{
					where: { id: req.params.id },
				},
				{ transaction: t }
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
exports.updateCabinet = catchAsync(async (req, res, next) => {
	const CabinetModel = getModel(req.headers["x-year"], "CabinetModel");
	try {
		await req.db.transaction(async (t) => {
			await CabinetModel.update(
				{ name: req.body.name, section_id: req.body.section_id },
				{
					where: { id: req.params.id },
					transaction: t,
				}
			);
		});
		res.status(200).json({
			state: "success",
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
