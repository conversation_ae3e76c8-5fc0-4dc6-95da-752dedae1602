const catchAsync = require("../utils/catchAsync");
const db = require("./../connection");
var fs = require("fs");
exports.getAllCategories = catchAsync(async (req, res, next) => {
	const categories = await db.query(`SELECT * FROM  categories`);
	res.status(200).json({
		state: "success",
		categories,
	});
});
exports.addCategory = catchAsync(async (req, res, next) => {
	const q = `INSERT INTO categories (name,parent_id) VALUES (?)`;
	const values = [req.body.name, req.body.parentCategory];
	const category = await db.query(q, [values]);

	if (!fs.existsSync(`D:/Archive/${category.insertId}`)) {
		fs.mkdirSync(`D:/Archive/${category.insertId}`);
	}
	res.status(200).json({
		state: "success",
	});
});
exports.editCategory = catchAsync(async (req, res, next) => {
	const q = `UPDATE categories SET name = ?,parent_id = ? WHERE id=${req.params.id} `;
	const value = [
		req.body.name,
		req.body.parentCategory === "" ? 0 : req.body.parentCategory,
	];
	await db.query(q, value);
	res.status(200).json({
		state: "success",
	});
});
