const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");
const { getModel } = require("../utils/modelSelect");

exports.getLastOutcomeSn = catchAsync(async (req, res, next) => {
	const OutcomeModel = getModel(req.headers["x-year"], "OutcomeModel");
	try {
		let outcome = await OutcomeModel.findOne({
			order: [["Sn", "DESC"]],
		});
		if (!outcome) {
			outcome = 0;
		} else {
			outcome = outcome.Sn;
		}
		res.status(200).json({
			state: "success",
			outcome: outcome,
		});
	} catch (error) {
		return next(new AppError(error, 500));
	}
});
// exports.getAlloutcomes = catchAsync(async (req, res, next) => {
// 	const outcomes = await db.query(`SELECT * FROM  outcomes ORDER BY num DESC`);

// 	res.status(200).json({
// 		state: "success",
// 		outcomes,
// 	});
// });
// exports.addOutcome = catchAsync(async (req, res, next) => {
// 	const q = `INSERT INTO outcomes (num, date, \`from\`, title, \`to\`, recipient, note) VALUES (?)`;
// 	const values = [
// 		+req.body.num,
// 		req.body.date,
// 		req.body.from,
// 		req.body.title,
// 		req.body.to,
// 		req.body.recipient,
// 		req.body.note,
// 	];
// 	income = await db.query(q, [values]);

// 	res.status(200).json({
// 		state: "success",
// 	});
// });
// exports.editOutcome = catchAsync(async (req, res, next) => {
// 	q = `UPDATE outcomes SET num = ?  , date =? ,\`from\`=? ,title=?,\`to\`=?,recipient=?,note=? WHERE id = ${+req
// 		.params.id}`;
// 	const values = [
// 		+req.body.num,
// 		req.body.date,
// 		req.body.from,
// 		req.body.title,
// 		req.body.to,
// 		req.body.recipient,
// 		req.body.note,
// 	];
// 	await db.query(q, values);

// 	res.status(200).json({
// 		state: "success",
// 	});
// });
// exports.deleteOutcome = catchAsync(async (req, res, next) => {
// 	await db.query(`DELETE FROM outcomes WHERE id=${req.params.id}`);

// 	res.status(200).json({
// 		state: "success",
// 	});
// });
