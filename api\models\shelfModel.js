// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const SectionModel = require("./sectionModel");
// const CabinetModel = require("./cabinetModel");
// const ShelfModel = sequelize.define("shelves", {
// 	name: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	section_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	cabinet_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// });
// ShelfModel.belongsTo(SectionModel, { foreignKey: "section_id" });
// ShelfModel.belongsTo(CabinetModel, { foreignKey: "cabinet_id" });
// module.exports = ShelfModel;

const { DataTypes } = require("sequelize");
function defineShelfModel(sequelize, models) {
	const ShelfModel = sequelize.define("shelf", {
		name: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		section_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
		cabinet_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
	});

	// Setup associations
	if (models) {
		if (models.SectionModel) {
			ShelfModel.belongsTo(models.SectionModel, {
				foreignKey: "section_id",
			});
		}
		if (models.CabinetModel) {
			ShelfModel.belongsTo(models.CabinetModel, {
				foreignKey: "cabinet_id",
			});
		}
	}

	return ShelfModel;
}

module.exports = defineShelfModel;
