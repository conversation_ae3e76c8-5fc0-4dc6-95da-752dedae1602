const express = require("express");

const usersController = require("./../controllers/usersController");
const authController = require("../controllers/authController");
const router = express.Router();

router
	.route("")
	.get(authController.restrictTo("super_admin"), usersController.getAllUsers);

router.route("/:id");
// .get(
// 	authController.restrictTo("super_admin"),
// 	usersController.getUserStations
// )
// .delete(authController.restrictTo("super_admin"), usersController.deleteUser)
// .patch(authController.restrictTo("super_admin"), usersController.updateUser);

module.exports = router;
