// const { DataTypes } = require("sequelize");
// const sequelize = require("./../connection");
// const SectionModel = require("./sectionModel");
// const CabinetModel = require("./cabinetModel");
// const ShelfModel = require("./shelfModel");
// const FolderModel = sequelize.define("folder", {
// 	name: {
// 		type: DataTypes.STRING,
// 		allowNull: false,
// 	},
// 	section_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	cabinet_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// 	shelf_id: {
// 		type: DataTypes.INTEGER,
// 		allowNull: false,
// 	},
// });
// FolderModel.belongsTo(SectionModel, { foreignKey: "section_id" });
// FolderModel.belongsTo(CabinetModel, { foreignKey: "cabinet_id" });
// FolderModel.belongsTo(ShelfModel, { foreignKey: "shelf_id" });
// module.exports = FolderModel;

const { DataTypes } = require("sequelize");
function defineFolderModel(sequelize, models) {
	const FolderModel = sequelize.define("folder", {
		name: {
			type: DataTypes.STRING,
			allowNull: false,
		},
		section_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
		cabinet_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
		shelf_id: {
			type: DataTypes.INTEGER,
			allowNull: false,
		},
	});

	// Setup associations
	if (models) {
		if (models.SectionModel) {
			FolderModel.belongsTo(models.SectionModel, {
				foreignKey: "section_id",
			});
		}
		if (models.CabinetModel) {
			FolderModel.belongsTo(models.CabinetModel, {
				foreignKey: "cabinet_id",
			});
		}
		if (models.ShelfModel) {
			FolderModel.belongsTo(models.ShelfModel, {
				foreignKey: "shelf_id",
			});
		}
	}

	return FolderModel;
}

module.exports = defineFolderModel;
