const catchAsync = require("../utils/catchAsync");

const fs = require("fs");
const { promisify } = require("util");
const exec = promisify(require("child_process").exec);

exports.getAllScaners = catchAsync(async (req, res, next) => {
	const scaners = await exec("naps2.console --listdevices --driver twain");
	res.status(200).json({
		state: "success",
		scaners,
	});
});
exports.scanPage = catchAsync(async (req, res, next) => {
	const scanProcess = await exec(
		`naps2.console -o D:/TEMP/${req.body.pageNum}.png --noprofile --driver twain --device ${req.body.scanner} --rotate ${req.body.rotate} --dpi ${req.body.dpi} --source ${req.body.source} --deskew`
	);
	const files = fs
		.readdirSync("D:/TEMP/", { withFileTypes: true })
		.filter((item) => !item.isDirectory())
		.map((item) => item.name);
	res.status(200).json({
		state: "success",
		files,
		scanProcess,
	});
});
exports.deletePage = catchAsync(async (req, res, next) => {
	fs.unlink(`D:/TEMP/${req.params.pageNum}.png`, (err) => {
		if (err) throw err;
	});
	res.status(200).json({
		state: "success",
	});
});
exports.deleteTemp = catchAsync(async (req, res, next) => {
	const files = fs.readdirSync("D:/TEMP/");
	console.log("files", files);
	files.forEach((file) => {
		fs.unlinkSync(`D:/TEMP/${file}`);
	});
	res.status(200).json({
		state: "success",
		message: "All files in TEMP folder have been deleted",
	});
});
