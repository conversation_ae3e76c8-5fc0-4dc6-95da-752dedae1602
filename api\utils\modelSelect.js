// modelSelector.js

const { sequelizeArchive } = require("../connection");

const defineCabinetModel = require("../models/cabinetModel");
const defineSectionModel = require("../models/sectionModel");
const defineShelfModel = require("../models/shelfModel");
const defineFolderModel = require("../models/folderModel");
const defineDocumentModel = require("../models/documentModel");
const defineIncomeModel = require("../models/incomeModel");
const defineOutcomeModel = require("../models/outcomeModel");
const definePermissionModel = require("../models/permissionModel");
const defineUserModel = require("../models/userModel");

function initModels(sequelize) {
	const sectionModel = defineSectionModel(sequelize);
	const cabintModel = defineCabinetModel(sequelize, {
		SectionModel: sectionModel,
	});
	const shelfModel = defineShelfModel(sequelize, {
		SectionModel: sectionModel,
		CabinetModel: cabintModel,
	});
	const folderModel = defineFolderModel(sequelize, {
		SectionModel: sectionModel,
		CabinetModel: cabintModel,
		ShelfModel: shelfModel,
	});
	const documentModel = defineDocumentModel(sequelize, {
		SectionModel: sectionModel,
		CabinetModel: cabintModel,
		ShelfModel: shelfModel,
		FolderModel: folderModel,
	});
	const incomeModel = defineIncomeModel(sequelize, {
		DocumentModel: documentModel,
	});
	const outcomeModel = defineOutcomeModel(sequelize, {
		DocumentModel: documentModel,
	});
	const permissionModel = definePermissionModel(sequelize);
	const userModel = defineUserModel(sequelize);
	return {
		CabinetModel: cabintModel,
		SectionModel: sectionModel,
		ShelfModel: shelfModel,
		FolderModel: folderModel,
		DocumentModel: documentModel,
		IncomeModel: incomeModel,
		OutcomeModel: outcomeModel,
		PermissionModel: permissionModel,
		UserModel: userModel,
	};
}

const models = {
	archive: initModels(sequelizeArchive),
	// "elec-2024": initModels(sequelizeElec2024),
};

// /**
//  * Gets a Sequelize model instance based on year and modelName
//  * @param {string} year - e.g., "2024"
//  * @param {string} modelName - e.g., "duesList"
//  * @returns Sequelize Model instance
//  */
function getModel(year, modelName) {
	const dbKey = year ? `archive-${year}` : "archive";

	if (!models[dbKey]) {
		throw new Error(`Database instance for year '${year}' not configured`);
	}
	if (!models[dbKey][modelName]) {
		throw new Error(`Model '${modelName}' not found for database '${dbKey}'`);
	}

	return models[dbKey][modelName];
}

module.exports = {
	getModel,
};
